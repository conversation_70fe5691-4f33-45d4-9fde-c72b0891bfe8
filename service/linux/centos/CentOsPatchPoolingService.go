package centos

import (
	"compress/gzip"
	"encoding/xml"
	"fmt"
	"io"
	"net/http"
	"os"
	"patch-central-repo/common"
	"patch-central-repo/logger"
	"patch-central-repo/model"
	"patch-central-repo/model/linux/centos"
	"patch-central-repo/repository"
	centosrepo "patch-central-repo/repository/linux/centos"
	"path/filepath"
	"strconv"
	"strings"
	"time"
)

type CentOsPatchPoolingService struct {
	PatchRepository       *centosrepo.CentOsPatchRepository
	ScanHistoryRepository *centosrepo.CentOsScanHistoryRepository
	SupportedOSRepository *repository.SupportedOSRepository
}

// XML structures for parsing repository metadata
type RepoMD struct {
	XMLName xml.Name   `xml:"repomd"`
	Data    []RepoData `xml:"data"`
}

type RepoData struct {
	Type     string       `xml:"type,attr"`
	Location RepoLocation `xml:"location"`
}

type RepoLocation struct {
	Href string `xml:"href,attr"`
}

// XML structures for parsing primary.xml
type Metadata struct {
	XMLName  xml.Name  `xml:"metadata"`
	Packages []Package `xml:"package"`
}

type Package struct {
	Type        string          `xml:"type,attr"`
	Name        string          `xml:"name"`
	Arch        string          `xml:"arch"`
	Version     PackageVersion  `xml:"version"`
	Checksum    PackageChecksum `xml:"checksum"`
	Summary     string          `xml:"summary"`
	Description string          `xml:"description"`
	Packager    string          `xml:"packager"`
	URL         string          `xml:"url"`
	Time        PackageTime     `xml:"time"`
	Size        PackageSize     `xml:"size"`
	Location    PackageLocation `xml:"location"`
	Format      PackageFormat   `xml:"format"`
}

type PackageVersion struct {
	Epoch string `xml:"epoch,attr"`
	Ver   string `xml:"ver,attr"`
	Rel   string `xml:"rel,attr"`
}

type PackageChecksum struct {
	Type  string `xml:"type,attr"`
	Value string `xml:",chardata"`
}

type PackageTime struct {
	File  string `xml:"file,attr"`
	Build string `xml:"build,attr"`
}

type PackageSize struct {
	Package   string `xml:"package,attr"`
	Installed string `xml:"installed,attr"`
	Archive   string `xml:"archive,attr"`
}

type PackageLocation struct {
	Href string `xml:"href,attr"`
}

type PackageFormat struct {
	License     string             `xml:"license"`
	Vendor      string             `xml:"vendor"`
	Group       string             `xml:"group"`
	BuildHost   string             `xml:"buildhost"`
	SourceRpm   string             `xml:"sourcerpm"`
	HeaderRange PackageHeaderRange `xml:"header-range"`
	Provides    PackageProvides    `xml:"provides"`
	Requires    PackageRequires    `xml:"requires"`
}

type PackageHeaderRange struct {
	Start string `xml:"start,attr"`
	End   string `xml:"end,attr"`
}

type PackageProvides struct {
	Entries []PackageEntry `xml:"entry"`
}

type PackageRequires struct {
	Entries []PackageEntry `xml:"entry"`
}

type PackageEntry struct {
	Name  string `xml:"name,attr"`
	Flags string `xml:"flags,attr"`
	Epoch string `xml:"epoch,attr"`
	Ver   string `xml:"ver,attr"`
	Rel   string `xml:"rel,attr"`
}

func NewCentOsPatchPoolingService() *CentOsPatchPoolingService {
	return &CentOsPatchPoolingService{
		PatchRepository:       centosrepo.NewCentOsPatchRepository(),
		ScanHistoryRepository: centosrepo.NewCentOsScanHistoryRepository(),
		SupportedOSRepository: repository.NewSupportedOSRepository(),
	}
}

// SyncCentOsPatches synchronizes CentOS patches from repositories
func (service *CentOsPatchPoolingService) SyncCentOsPatches() {
	logger.ServiceLogger.Info("Process started to Sync CentOS Patches")

	maxRetryCount := 3
	totalNewPatch := 0

	// Download and process repository files
	service.DownloadRepoFile(maxRetryCount, totalNewPatch)

	logger.ServiceLogger.Info("Process completed to Sync CentOS Patches")
}

// DownloadRepoFile downloads repository files and processes patches
func (service *CentOsPatchPoolingService) DownloadRepoFile(maxRetryCount, totalNewPatch int) {
	logger.ServiceLogger.Info("START -- fetching the available patches of CentOS")
	logger.ServiceLogger.Info("Downloading all file list")

	linuxFolder := service.getLinuxDataFolder()

	supportedOS, err := service.SupportedOSRepository.FindByOsPlatform(common.UNIX_CENTOS)
	if err != nil {
		logger.ServiceLogger.Error("Error getting supported OS configuration: ", err)
		// Create default configuration if not found
		supportedOS = service.createDefaultSupportedOS()
		_, err = service.SupportedOSRepository.Save(supportedOS)
		if err != nil {
			logger.ServiceLogger.Error("Error saving default supported OS configuration: ", err)
			return
		}
	}

	// Download repomd.xml files for all supported versions and directories
	for _, osVersion := range supportedOS.SupportedRepoForOs {
		osVersion = strings.TrimSpace(osVersion)
		repoDir := supportedOS.DirStructure
		for _, dir := range supportedOS.SupportedDirs {
			url := strings.ReplaceAll(repoDir, "${version}", osVersion)
			url = strings.ReplaceAll(url, "${dirtype}", dir)
			centosDir := filepath.Join(linuxFolder, common.UNIX_CENTOS.GetDirName(), url)

			logger.ServiceLogger.Debug("CentOS dir path: ", centosDir)

			// Create directory if it doesn't exist
			err := os.MkdirAll(centosDir, 0755)
			if err != nil {
				logger.ServiceLogger.Error("Error creating directory: ", err)
				continue
			}

			// Download repomd.xml and repomd.xml.asc
			baseUrl := supportedOS.MirrorAlternativUrls[0]
			service.downloadFile(centosDir, "repomd.xml", osVersion, url, baseUrl)
			service.downloadFile(centosDir, "repomd.xml.asc", osVersion, url, baseUrl)
		}
	}

	// Process repomd.xml files to get primary.xml locations
	for _, osVersion := range supportedOS.SupportedRepoForOs {
		osVersion = strings.TrimSpace(osVersion)
		service.processRepomdXmlFile(osVersion, supportedOS.SupportedDirs,
			supportedOS.DirStructure, supportedOS.MirrorAlternativUrls[0])
	}

	// Process all primary.xml files
	service.processAllPrimaryFiles(maxRetryCount, totalNewPatch)

	// Resolve dependencies
	resolveDepPkgIdList, err := service.PatchRepository.GetUnresolvedDepPkgIdList()
	if err != nil {
		logger.ServiceLogger.Error("Error getting unresolved dependencies: ", err)
	} else {
		service.resolveDependencies(resolveDepPkgIdList)
	}

	logger.ServiceLogger.Info("Process completed to fetch CentOS patches")
}

// getLinuxDataFolder returns the Linux data folder path
func (service *CentOsPatchPoolingService) getLinuxDataFolder() string {
	// Default to current directory + filedb/linux
	currentDir, _ := os.Getwd()
	return filepath.Join(currentDir, "filedb", "linux")
}

// createDefaultSupportedOS creates default supported OS configuration for CentOS
func (service *CentOsPatchPoolingService) createDefaultSupportedOS() *model.SupportedOS {
	supportedOS := &model.SupportedOS{
		OsName:               "Centos",
		OsVersion:            "all",
		OsPlatform:           common.UNIX_CENTOS,
		DirStructure:         "/${version}/${dirtype}/x86_64/os/repodata/",
		SupportedRepoForOs:   []string{"9-stream", "10-stream"},
		SupportedDirs:        []string{"BaseOS", "AppStream"},
		MirrorAlternativUrls: []string{"https://mirror.stream.centos.org"},
		SupportedArch:        []string{"x86_64", "noarch"},
	}
	supportedOS.CreatedTime = time.Now().UnixMilli()
	supportedOS.UpdatedTime = time.Now().UnixMilli()
	return supportedOS
}

// downloadFile downloads a file from the repository
func (service *CentOsPatchPoolingService) downloadFile(centosDir, fileName, osVersion, url, repoUrl string) {
	finalUrl := repoUrl + url + fileName
	packageFolder := filepath.Join(centosDir, fileName)

	logger.ServiceLogger.Debug("Downloading file: ", finalUrl)

	resp, err := http.Get(finalUrl)
	if err != nil {
		logger.ServiceLogger.Error("Error downloading file ", finalUrl, ": ", err)
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		logger.ServiceLogger.Error("HTTP error downloading file ", finalUrl, ": ", resp.StatusCode)
		return
	}

	// Create the file
	out, err := os.Create(packageFolder)
	if err != nil {
		logger.ServiceLogger.Error("Error creating file ", packageFolder, ": ", err)
		return
	}
	defer out.Close()

	// Copy the response body to the file
	_, err = io.Copy(out, resp.Body)
	if err != nil {
		logger.ServiceLogger.Error("Error writing file ", packageFolder, ": ", err)
		return
	}

	logger.ServiceLogger.Debug("Download complete file ", fileName, " of os version ", osVersion)
}

// processRepomdXmlFile processes repomd.xml to get primary.xml location and downloads it
func (service *CentOsPatchPoolingService) processRepomdXmlFile(osVersion string, supportedDirs []string, repoDir, repoUrl string) {
	linuxFolder := service.getLinuxDataFolder()

	for _, dir := range supportedDirs {
		dirPath := strings.ReplaceAll(repoDir, "${version}", osVersion)
		dirPath = strings.ReplaceAll(dirPath, "${dirtype}", dir)

		xmlPath := filepath.Join(linuxFolder, common.UNIX_CENTOS.GetDirName(), dirPath, "repomd.xml")
		centosDir := filepath.Join(linuxFolder, common.UNIX_CENTOS.GetDirName(), dirPath)

		// Parse repomd.xml to get primary.xml location
		primaryXmlLocation, err := service.parsePrimaryXmlLocation(xmlPath)
		if err != nil {
			logger.ServiceLogger.Error("Error parsing repomd.xml: ", err)
			continue
		}

		if primaryXmlLocation != "" {
			// Download primary.xml.gz
			finalUrl := repoUrl + strings.TrimSuffix(dirPath, "repodata/") + primaryXmlLocation
			primaryXmlPath := filepath.Join(centosDir, filepath.Base(primaryXmlLocation))

			logger.ServiceLogger.Debug("Downloading primary.xml from: ", finalUrl)

			resp, err := http.Get(finalUrl)
			if err != nil {
				logger.ServiceLogger.Error("Error downloading primary.xml: ", err)
				continue
			}
			defer resp.Body.Close()

			if resp.StatusCode != http.StatusOK {
				logger.ServiceLogger.Error("HTTP error downloading primary.xml: ", resp.StatusCode)
				continue
			}

			// Create the file
			out, err := os.Create(primaryXmlPath)
			if err != nil {
				logger.ServiceLogger.Error("Error creating primary.xml file: ", err)
				continue
			}
			defer out.Close()

			// Copy the response body to the file
			_, err = io.Copy(out, resp.Body)
			if err != nil {
				logger.ServiceLogger.Error("Error writing primary.xml file: ", err)
				continue
			}

			logger.ServiceLogger.Debug("Downloaded primary.xml for ", osVersion, "/", dir)
		}
	}
}

// parsePrimaryXmlLocation parses repomd.xml to extract primary.xml location
func (service *CentOsPatchPoolingService) parsePrimaryXmlLocation(xmlPath string) (string, error) {
	file, err := os.Open(xmlPath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	var repoMD RepoMD
	decoder := xml.NewDecoder(file)
	err = decoder.Decode(&repoMD)
	if err != nil {
		return "", err
	}

	// Find primary data location
	for _, data := range repoMD.Data {
		if data.Type == "primary" {
			return data.Location.Href, nil
		}
	}

	return "", fmt.Errorf("primary data not found in repomd.xml")
}

// processAllPrimaryFiles processes all downloaded primary.xml.gz files
func (service *CentOsPatchPoolingService) processAllPrimaryFiles(maxRetryCount, totalNewPatch int) {
	linuxFolder := service.getLinuxDataFolder()

	if maxRetryCount <= 0 {
		return
	}

	supportedOS, err := service.SupportedOSRepository.FindByOsPlatform(common.UNIX_CENTOS)
	if err != nil || supportedOS == nil {
		logger.ServiceLogger.Error("Error getting supported OS configuration: ", err)
		return
	}

	for _, osVersion := range supportedOS.SupportedRepoForOs {
		osVersion = strings.TrimSpace(osVersion)
		repoDir := supportedOS.DirStructure

		for _, dir := range supportedOS.SupportedDirs {
			url := strings.ReplaceAll(repoDir, "${version}", osVersion)
			url = strings.ReplaceAll(url, "${dirtype}", dir)
			centosDir := filepath.Join(linuxFolder, common.UNIX_CENTOS.GetDirName(), url)

			logger.ServiceLogger.Debug("CentOS dir path: ", centosDir)

			// Check if directory exists
			if _, err := os.Stat(centosDir); os.IsNotExist(err) {
				continue
			}

			// Find primary.xml.gz file
			files, err := os.ReadDir(centosDir)
			if err != nil {
				logger.ServiceLogger.Error("Error reading directory: ", err)
				continue
			}

			var compressedFile string
			for _, file := range files {
				if strings.Contains(file.Name(), "primary.xml.gz") {
					compressedFile = filepath.Join(centosDir, file.Name())
					break
				}
			}

			if compressedFile == "" {
				logger.ServiceLogger.Debug("No primary.xml.gz file found in: ", centosDir)
				continue
			}

			// Check if this file has already been processed
			scanHistory, err := service.ScanHistoryRepository.FindByXmlName(filepath.Base(compressedFile))
			if err == nil && scanHistory != nil {
				logger.ServiceLogger.Debug("File already processed: ", compressedFile)
				continue
			}

			// Decompress and process the file
			xmlFile := strings.TrimSuffix(compressedFile, ".gz")
			err = service.decompressGzipFile(compressedFile, xmlFile)
			if err != nil {
				logger.ServiceLogger.Error("Error decompressing file: ", err)
				continue
			}

			// Process the XML file
			service.processRepoFileAndCreatePatchData(totalNewPatch, osVersion, xmlFile, url, supportedOS, compressedFile)

			// Clean up decompressed file
			os.Remove(xmlFile)
		}
	}
}

// decompressGzipFile decompresses a gzip file
func (service *CentOsPatchPoolingService) decompressGzipFile(src, dst string) error {
	srcFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer srcFile.Close()

	gzipReader, err := gzip.NewReader(srcFile)
	if err != nil {
		return err
	}
	defer gzipReader.Close()

	dstFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer dstFile.Close()

	_, err = io.Copy(dstFile, gzipReader)
	return err
}

// processRepoFileAndCreatePatchData processes primary.xml and creates patch data
func (service *CentOsPatchPoolingService) processRepoFileAndCreatePatchData(totalNewPatch int, osVersion, packageFileXml, url string, supportedOS *model.SupportedOS, compressedFile string) {
	file, err := os.Open(packageFileXml)
	if err != nil {
		logger.ServiceLogger.Error("Error opening XML file: ", err)
		return
	}
	defer file.Close()

	var metadata Metadata
	decoder := xml.NewDecoder(file)
	err = decoder.Decode(&metadata)
	if err != nil {
		logger.ServiceLogger.Error("Error parsing XML file: ", err)
		return
	}

	logger.ServiceLogger.Debug("Found total ", len(metadata.Packages), " packages in the xml")
	distribution := "CentOs " + osVersion

	for _, pkg := range metadata.Packages {
		if pkg.Type == "rpm" {
			_, err := service.createPatchEntityFromPackage(pkg, distribution, url, supportedOS.MirrorAlternativUrls[0])
			if err != nil {
				logger.ServiceLogger.Error("Error creating patch entity: ", err)
			}
		}
	}

	// Create history record
	service.createHistoryData(osVersion, filepath.Base(compressedFile))
}

// createPatchEntityFromPackage creates a patch entity from package data
func (service *CentOsPatchPoolingService) createPatchEntityFromPackage(pkg Package, distribution, url, repoUrl string) (*centos.CentOsPatch, error) {
	// Generate package ID
	pkgId := fmt.Sprintf("%s-%s-%s.%s", pkg.Name, pkg.Version.Ver, pkg.Version.Rel, pkg.Arch)

	// Check if patch already exists
	existingPatch, err := service.PatchRepository.FindByPkgId(pkgId)
	if err == nil && existingPatch != nil && existingPatch.Id != 0 {
		logger.ServiceLogger.Debug("Patch already exists: ", pkgId)
		return existingPatch, nil
	}

	// Create new patch
	patch := &centos.CentOsPatch{
		PkgId:          pkgId,
		PkgName:        pkg.Name,
		Arch:           pkg.Arch,
		Version:        pkg.Version.Ver,
		ReleaseVersion: pkg.Version.Rel,
		Description:    pkg.Description,
		Distribution:   distribution,
		Title:          pkg.Summary,
		BinaryPkgName:  pkg.Name,
		SrcPkgName:     pkg.Format.SourceRpm,
		DownloadUrl:    repoUrl + url + pkg.Location.Href,
	}

	// Parse package size
	if pkg.Size.Package != "" {
		if size, err := strconv.ParseInt(pkg.Size.Package, 10, 64); err == nil {
			patch.PkgSize = size
		}
	}

	// Parse build time
	if pkg.Time.Build != "" {
		if buildTime, err := strconv.ParseInt(pkg.Time.Build, 10, 64); err == nil {
			patch.ReleaseDate = buildTime * 1000 // Convert to milliseconds
		}
	}

	// Process dependencies
	if len(pkg.Format.Requires.Entries) > 0 {
		dependencies := service.formatDependencies(pkg.Format.Requires.Entries)
		patch.Dependencies = dependencies
	}

	patch.CreatedTime = time.Now().UnixMilli()
	patch.UpdatedTime = time.Now().UnixMilli()

	savedPatch, err := service.PatchRepository.Save(patch)
	if err != nil {
		logger.ServiceLogger.Error("Error creating patch: ", err)
		return nil, err
	}

	logger.ServiceLogger.Debug("Created patch id: ", savedPatch.Id, ", pkg id: ", savedPatch.PkgId, ", pkg name: ", savedPatch.PkgName)
	return savedPatch, nil
}

// formatDependencies formats package dependencies
func (service *CentOsPatchPoolingService) formatDependencies(entries []PackageEntry) string {
	var dependencies []string
	for _, entry := range entries {
		dep := entry.Name
		if entry.Ver != "" {
			dep += " " + entry.Ver
		}
		if entry.Rel != "" {
			dep += "-" + entry.Rel
		}
		dependencies = append(dependencies, dep)
	}
	return strings.Join(dependencies, ",")
}

// createHistoryData creates scan history record
func (service *CentOsPatchPoolingService) createHistoryData(osVersion, xmlName string) error {
	history := &centos.CentOsScanHistory{
		XmlName:   xmlName,
		OsVersion: osVersion,
		ScanTime:  time.Now().UnixMilli(),
	}
	history.CreatedTime = time.Now().UnixMilli()
	history.UpdatedTime = time.Now().UnixMilli()

	_, err := service.ScanHistoryRepository.Save(history)
	if err != nil {
		logger.ServiceLogger.Error("Error creating scan history: ", err)
		return err
	}

	logger.ServiceLogger.Debug("Saved history of xml file: ", xmlName)
	return nil
}

// resolveDependencies resolves package dependencies
func (service *CentOsPatchPoolingService) resolveDependencies(resolveDepPkgIdList []string) {
	if len(resolveDepPkgIdList) == 0 {
		logger.ServiceLogger.Debug("No patches found to resolve the dependencies")
		return
	}

	logger.ServiceLogger.Debug("Found total patches to resolve the dependency: ", len(resolveDepPkgIdList))

	// Process in chunks of 200
	chunkSize := 200
	for i := 0; i < len(resolveDepPkgIdList); i += chunkSize {
		end := i + chunkSize
		if end > len(resolveDepPkgIdList) {
			end = len(resolveDepPkgIdList)
		}
		chunk := resolveDepPkgIdList[i:end]

		patchList, err := service.PatchRepository.GetListByPkgId(chunk)
		if err != nil {
			logger.ServiceLogger.Error("Error getting patch list by pkg id: ", err)
			continue
		}

		for _, patch := range patchList {
			logger.ServiceLogger.Debug("Resolving the dependencies of package id: ", patch.PkgId)
			rawDependencies := patch.Dependencies
			logger.ServiceLogger.Debug("Found dependencies to resolve: ", rawDependencies)

			if rawDependencies != "" {
				pkgIdList := service.processDependencies(rawDependencies, patch.Arch, patch.Distribution)
				if len(pkgIdList) > 0 {
					logger.ServiceLogger.Debug("Saving dependency pkg ids of pkgid=", patch.PkgId)
					patch.DependenciesPkgId = strings.Join(pkgIdList, ",")
					_, err := service.PatchRepository.Save(&patch)
					if err != nil {
						logger.ServiceLogger.Error("Error saving patch with dependencies: ", err)
					}
				}
			}
		}
	}
}

// processDependencies processes package dependencies and returns resolved package IDs
func (service *CentOsPatchPoolingService) processDependencies(rawDependencies, arch, distribution string) []string {
	var pkgIdList []string

	if rawDependencies == "" {
		return pkgIdList
	}

	dependencies := strings.Split(rawDependencies, ",")
	for _, dep := range dependencies {
		dep = strings.TrimSpace(dep)
		if dep == "" {
			continue
		}

		// Try to find matching packages
		patches, err := service.PatchRepository.FindByNameAndArchAndDistribution(dep, arch, distribution)
		if err != nil {
			logger.ServiceLogger.Debug("Error finding dependency: ", dep, " - ", err)
			continue
		}

		for _, patch := range patches {
			pkgIdList = append(pkgIdList, patch.PkgId)
		}
	}

	return pkgIdList
}
