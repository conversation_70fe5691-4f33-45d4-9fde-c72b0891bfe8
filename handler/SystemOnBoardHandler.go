package handler

import (
	"patch-central-repo/logger"
	"patch-central-repo/rest"
	"patch-central-repo/service"
	scheduler "patch-central-repo/service/job"
	"patch-central-repo/service/linux/centos"
	"patch-central-repo/service/windows"
	"runtime"
)

func SystemOnBoardService() {
	createAdminUser()
	go func() {
		syncPatches()
	}()
	go scheduler.NewJobExecutorService().Init()
}

func createAdminUser() {
	logger.ServiceLogger.Info("Process started to create admin user")
	user, _ := service.NewUserService().GetUserById(1, false)
	if user.Id == 0 {
		userRest := rest.UserRest{
			BaseEntityRest: rest.BaseEntityRest{Name: "admin"},
			Password:       "admin",
			Email:          "admin",
		}

		_, err := service.NewUserService().Create(userRest)
		if err.Message != "" {
			logger.ServiceLogger.Warn("admin user not created : ", err.Message)
		} else {
			logger.ServiceLogger.Debug("admin user created successfully.")
		}
	}
	logger.ServiceLogger.Info("Process Completed to create admin user")
}

func syncPatches() {
	if runtime.GOOS == "windows" { //to import patch in wsus from update catalog which run only in windows
		windows.NewWindowsPatchService().SyncWindowsPatchFromUpdateCatalog()
		windows.NewWindowsPatchService().SyncWindowsDotNetPatchFromUpdateCatalog()
	} else {
		logger.ServiceLogger.Debug("[SystemOnBoardService] starting repository syncing...")
		/*go thirdparty.NewThirdPartyPackageService().SyncThirdPartyPatchRepo()
		go windows.NewWindowsPatchService().SyncWindowsPatches()
		go ubuntu.NewUbuntuNoticeDataService().SyncUbuntuNoticeData()
		go ubuntu.NewUbuntuPatchService().SyncUbuntuMirrorPkg()*/
		go centos.NewCentOsPatchPoolingService().SyncCentOsPatches()
		//go macos.NewMacOsPatchService().SyncMacOsPatches()
		logger.ServiceLogger.Debug("[SystemOnBoardService] repository syncing completed...")
	}
}
